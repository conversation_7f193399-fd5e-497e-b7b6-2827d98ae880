# 合并请求代码审查工具

这是一个Python工具，可以从阿里云CodeUp的合并请求URL自动获取代码diff并进行AI代码审查，支持自动发表评论到云效平台。

## 功能特性

1. **URL解析**: 自动解析CodeUp合并请求URL
2. **Repository查找**: 根据项目路径自动查找repository ID
3. **智能版本选择**: 自动处理合并请求的多个版本，选择最新版本与基础版本进行对比
4. **Diff获取**: 获取代码变更的详细diff信息
5. **AI代码审查**: 使用AI模型进行专业的代码审查，重点关注严重Bug、安全漏洞、性能问题和架构缺陷
6. **自动评论**: 支持自动将审查结果发表为云效合并请求的行内评论和全局评论
7. **最终总结**: 自动生成包含文件列表、必须修改问题、次要问题的总结报告
8. **自定义模型**: 支持通过命令行参数指定AI模型
9. **自定义提示词**: 支持从本地文件加载系统提示词
10. **批量处理**: 支持一次处理多个合并请求URL
11. **文件过滤**: 支持配置排除模式，跳过不需要审查的文件
12. **完整文件上下文**: 获取文件完整内容以提供更准确的审查结果

## 快速安装

### 自动安装（推荐）

我们提供了跨平台的自动安装脚本，会自动检查依赖、配置环境变量：

**Linux/macOS:**
```bash
./setup.sh
```

**Windows (命令提示符):**
```cmd
setup.bat
```

**Windows (PowerShell):**
```powershell
.\setup.ps1
```

安装脚本会自动：
- 检查Python和pip环境
- 安装项目依赖
- 检查并配置必需的环境变量
- 设置快捷命令（Linux/macOS支持`cr`命令）

### 手动安装

如果需要手动安装，请按以下步骤操作：

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 配置环境变量（见下方环境配置部分）

## 环境配置

### 必需环境变量

**阿里云访问密钥（必需）：**
```bash
export ALIBABA_CLOUD_ACCESS_KEY_ID="your-access-key-id"
export ALIBABA_CLOUD_ACCESS_KEY_SECRET="your-access-key-secret"
```

**LLM API密钥（必需）：**
```bash
export XM_LLM_API_KEY="your-api-key"
```

> 💡 **获取API密钥**: 您可以访问 [https://litellm-test.summerfarm.net/v1](https://litellm-test.summerfarm.net/v1) 获取LLM API密钥。

### 可选环境变量

```bash
# LLM配置
export XM_LLM_BASE_URL="https://litellm-test.summerfarm.net/v1"
export XM_LLM_MODEL="deepseek-v3-250324"

# 评论功能控制
export XM_NEED_COMMENT="true"  # 设置为false可禁用自动评论功能

# 文件排除模式（逗号分隔）
export MR_REVIEW_EXCLUDE_PATTERNS="test/java/,*Test.java,*.md,*.txt,*.json"
```

### 环境变量配置说明

- **ALIBABA_CLOUD_ACCESS_KEY_ID/SECRET**: 用于访问阿里云CodeUp API，可在阿里云控制台 → AccessKey管理中获取
- **XM_LLM_API_KEY**: 用于AI代码审查功能的API密钥
- **XM_LLM_BASE_URL**: LLM服务的基础URL（可选，有默认值）
- **XM_LLM_MODEL**: 使用的AI模型名称（可选，有默认值）
- **XM_NEED_COMMENT**: 是否自动发表评论到云效（可选，默认为true）
- **MR_REVIEW_EXCLUDE_PATTERNS**: 排除文件的模式列表（可选，有默认值）

> 📝 **注意**: 使用自动安装脚本时，如果检测到环境变量未设置，会自动提示您输入并保存到配置文件中。

## 使用方法

### 快捷命令使用（推荐）

如果使用了自动安装脚本，在Linux/macOS系统上可以直接使用`cr`快捷命令：

```bash
# 直接使用单个URL
cr "https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255/diffs"

# 批量处理多个URL（逗号分隔）
cr "https://codeup.aliyun.com/.../change/255/diffs,https://codeup.aliyun.com/.../change/256/diffs"

# 交互式输入URL
cr
```

### 完整命令使用

#### 交互式使用

1. 运行程序：
```bash
python mr_code_review.py
```

2. 输入CodeUp合并请求URL，支持多个URL用逗号分隔：
```
https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255/diffs
```

#### 命令行使用

```bash
# 使用默认模型审查单个URL
python mr_code_review.py --url "https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255/diffs"

# 批量处理多个URL
python mr_code_review.py --url "https://codeup.aliyun.com/.../change/255/diffs,https://codeup.aliyun.com/.../change/256/diffs"

# 指定特定模型
python mr_code_review.py --model "gpt-4" --url "https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255/diffs"

# 仅指定模型（交互式输入URL）
python mr_code_review.py --model "claude-3-sonnet"

# 查看帮助信息
python mr_code_review.py --help
```

### 命令行参数

- `--model MODEL`: 指定AI模型（可选，默认使用环境变量 `XM_LLM_MODEL`）
- `--url URL`: 指定CodeUp合并请求URL，支持多个URL用逗号分隔（可选，不指定则交互式输入）
- `--help`: 显示帮助信息

## 程序执行流程

程序会自动执行以下步骤：

1. **URL解析**: 解析URL获取组织ID、项目路径和合并请求ID
2. **Repository查找**: 查找对应的repository ID
3. **版本获取**: 获取合并请求中的所有commit版本
4. **智能选择**: 选择最新版本（MERGE_SOURCE）与基础版本（MERGE_TARGET）进行对比
5. **Diff获取**: 获取代码变更的详细diff
6. **文件过滤**: 根据排除模式跳过不需要审查的文件
7. **上下文获取**: 获取每个变更文件的完整内容以提供更好的审查上下文
8. **AI审查**: 对每个变更文件进行AI代码审查
9. **自动评论**: 将审查结果发表为云效合并请求的行内评论
10. **最终总结**: 生成并发表包含文件列表、必须修改问题、次要问题的总结报告

## 自定义系统提示词

工具会自动从 `code_review_instruction.txt` 文件加载系统提示词。你可以编辑这个文件来自定义AI的审查行为：

1. 编辑 `code_review_instruction.txt` 文件
2. 重新运行工具，新的提示词会自动生效

如果文件不存在，工具会使用内置的默认提示词。

## 多版本支持

当合并请求包含多个版本时（例如用户在创建合并请求后又有新的提交），工具会智能处理：

### 版本选择策略
- **MERGE_SOURCE（新代码）**: 自动选择版本号最大的版本（最新的提交）
- **MERGE_TARGET（旧代码）**: 自动选择版本号最小的版本（通常是master分支的基础版本）

### 示例场景
假设合并请求包含以下版本：
```
MERGE_SOURCE:
  - 版本1: commit_a3163373 (初始提交)
  - 版本2: commit_f6f26c09 (后续修改)

MERGE_TARGET:
  - Base: commit_99bf7fa8 (master分支基础)
```

工具会自动选择：
- **对比源**: 版本2 (commit_f6f26c09) - 最新的代码变更
- **对比目标**: Base (commit_99bf7fa8) - master分支基础

这确保了审查的是最新的完整变更内容。

## 自动评论功能

工具支持自动将审查结果发表到云效合并请求中：

### 行内评论
- 对每个有问题的文件发表行内评论
- 自动定位到合适的代码行
- 如果未发现严重问题，会自动标记评论为已解决

### 全局评论
- 发表包含完整审查总结的全局评论
- 包含文件列表、必须修改问题、次要问题等
- 包含审查时间和使用的AI模型信息

### 控制评论功能
```bash
# 启用自动评论（默认）
export XM_NEED_COMMENT="true"

# 禁用自动评论
export XM_NEED_COMMENT="false"
```

## 文件过滤

工具支持配置排除模式，跳过不需要审查的文件：

### 默认排除模式
- `test/java/` - 测试目录
- `*Test.java` - 测试文件
- `*.md` - Markdown文档
- `*.txt` - 文本文件
- `*.json` - JSON配置文件

### 自定义排除模式
```bash
export MR_REVIEW_EXCLUDE_PATTERNS="test/,*Test.java,*.md,*.yml,build/"
```

## URL格式说明

支持的URL格式：
```
https://codeup.aliyun.com/{organization_id}/{namespace}/{project_name}/change/{merge_request_id}/diffs
```

例如：
- `organization_id`: 6189f099041d450d2c253abc
- `namespace`: tech
- `project_name`: xianmu-ai
- `merge_request_id`: 255

## 技术架构

### 核心组件

1. **URLParser**: URL解析器，提取合并请求信息
2. **DevOpsClient**: 阿里云DevOps API客户端封装
3. **AICodeReviewer**: AI代码审查器，支持流式响应
4. **评论系统**: 自动发表行内评论和全局评论

### 技术特点

- **流式AI响应**: 实时显示AI审查过程
- **完善错误处理**: URL验证、API调用异常处理、网络超时处理
- **智能评论定位**: 自动解析diff内容定位评论行号
- **批量处理**: 支持同时处理多个合并请求
- **可配置性**: 丰富的环境变量配置选项

## 注意事项

1. **环境变量配置**: 必须配置阿里云访问密钥（ALIBABA_CLOUD_ACCESS_KEY_ID/SECRET）和LLM API密钥（XM_LLM_API_KEY）
2. **权限要求**: 确保有访问对应CodeUp项目的权限
3. **审查重点**: AI审查重点关注严重Bug、安全漏洞、性能问题和架构缺陷
4. **代码风格**: 程序会忽略次要的代码风格问题
5. **提示词文件**: 系统提示词文件 `code_review_instruction.txt` 应与脚本在同一目录
6. **自动安装**: 推荐使用自动安装脚本，会自动检查和配置所有必需的环境变量
7. **API密钥获取**: LLM API密钥可从 https://litellm-test.summerfarm.net/v1 获取
8. **评论权限**: 确保配置的阿里云账号有向目标项目发表评论的权限

## 错误处理

程序包含完善的错误处理机制：
- URL格式验证
- API调用异常处理
- 网络请求超时处理
- 空数据检查
- 批量处理中的单个URL错误不会影响其他URL的处理

如果遇到问题，请检查：
1. URL格式是否正确
2. 网络连接是否正常
3. 阿里云凭据是否配置正确
4. 是否有访问项目的权限
5. 系统提示词文件是否存在且可读
6. 是否有向项目发表评论的权限

## 扩展功能建议

1. **更多输出格式**: 支持HTML、PDF等格式的报告
2. **自定义审查规则**: 支持更细粒度的审查规则配置
3. **CI/CD集成**: 可集成到持续集成流程中
4. **审查历史**: 支持审查历史记录和趋势分析
5. **团队协作**: 支持多人协作审查和审查分配

## 项目文件结构

```
mr-code-review/
├── mr_code_review.py          # 主程序文件
├── requirements.txt           # Python依赖包列表
├── setup.sh                   # Linux/macOS自动安装脚本
├── setup.bat                  # Windows命令提示符安装脚本
├── setup.ps1                  # Windows PowerShell安装脚本
├── code_review_instruction.txt # 系统提示词文件（可选）
├── README.md                  # 本文档
└── .gitignore                 # Git忽略文件配置
```

## 总结

这个工具成功实现了完整的代码审查自动化流程：
- ✅ 从URL解析合并请求信息
- ✅ 智能版本选择和diff获取
- ✅ AI驱动的专业代码审查
- ✅ 自动评论发表到云效平台
- ✅ 批量处理和文件过滤
- ✅ 完善的错误处理和用户体验
- ✅ 跨平台支持和自动安装

程序结构清晰，功能完整，具有良好的可扩展性和用户体验。